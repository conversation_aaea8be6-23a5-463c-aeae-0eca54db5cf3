# Changelog

## [3.0.5](https://github.com/viarotel-org/vite-uniapp-template/compare/v3.0.4...v3.0.5) (2025-09-02)


### Bug Fixes

* 🐛 解决 Windows 下云打包编译阶段卡死的问题 ([1b57707](https://github.com/viarotel-org/vite-uniapp-template/commit/1b57707dca3727b2c72ba948d9e6caf4712e08a4))

## [3.0.4](https://github.com/viarotel-org/vite-uniapp-template/compare/v3.0.3...v3.0.4) (2025-07-10)


### Bug Fixes

* 🐛 修复 windows 下静态资源插件路径转换异常的问题 ([ac3059f](https://github.com/viarotel-org/vite-uniapp-template/commit/ac3059f464d0fe82005d75c9e2cb27ca7556eeca))


### Performance Improvements

* 🔍️ 调整依赖项以解决 APP 端兼容性问题 ([d001f74](https://github.com/viarotel-org/vite-uniapp-template/commit/d001f747e8620e3965506110d3629121b79fd790))
* 🚀 优化样式加载逻辑 ([6679418](https://github.com/viarotel-org/vite-uniapp-template/commit/667941827ca0c367a8982b733e6cc9706c4db635))

## [3.0.3](https://github.com/viarotel-org/vite-uniapp-template/compare/v3.0.2...v3.0.3) (2025-05-30)


### Bug Fixes

* 🐛 修复 APP 环境下依赖不兼容白屏问题 ([b892d59](https://github.com/viarotel-org/vite-uniapp-template/commit/b892d59bcde6d3f0e1bb7fc9f14596993d8958bd))

## [3.0.2](https://github.com/viarotel-org/vite-uniapp-template/compare/v3.0.1...v3.0.2) (2025-05-29)


### Performance Improvements

* 🚀 规范变量及目录结构命名 ([a636a67](https://github.com/viarotel-org/vite-uniapp-template/commit/a636a67c34325421a7a139b38cd7974a9a219b43))

## [3.0.1](https://github.com/viarotel-org/vite-uniapp-template/compare/v3.0.0...v3.0.1) (2025-05-29)


### Performance Improvements

* 🌐 专注于使用 pnpm 包管理器 ([07679c8](https://github.com/viarotel-org/vite-uniapp-template/commit/07679c8716692cb09571a05855edec1bddb5210e))

## [3.0.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.7.3...v3.0.0) (2025-05-28)


### Features

* 🚀 对演示页面进行重新设计 ([8b07348](https://github.com/viarotel-org/vite-uniapp-template/commit/8b07348a6bc2f858a450249cbf4b3bcc09dec6b2))
* 🚀 更新主页 ([5fcf0fe](https://github.com/viarotel-org/vite-uniapp-template/commit/5fcf0fe53820799e6d70aba78c845fc652aa4fc9))
* 🚀 添加通用列表页面演示 ([a7ff94b](https://github.com/viarotel-org/vite-uniapp-template/commit/a7ff94badba52211507ff7faa8ade7e40c64b7ec))


### Bug Fixes

* 🚀 修复小程序使用 Unocss 不支持渐变的问题 ([c001a05](https://github.com/viarotel-org/vite-uniapp-template/commit/c001a05d7a73b3d7b02e5f01c5615adf3252d0bb))


### Performance Improvements

* 💄 对登录页面进行重新设计 ([84b77f4](https://github.com/viarotel-org/vite-uniapp-template/commit/84b77f45f31032bf9f9a02b7136d45d67d284160))
* 🚀 Update ([527e84d](https://github.com/viarotel-org/vite-uniapp-template/commit/527e84d7604fbbb4fba7c9bc6264bd4379ae9a5e))
* 🚀 优化分包目录架构 ([06ca08f](https://github.com/viarotel-org/vite-uniapp-template/commit/06ca08fb5c630b4f3b35964626ab6e92e7d473cd))
* 🚀 优化生产环境细节 ([d7870d4](https://github.com/viarotel-org/vite-uniapp-template/commit/d7870d4403730f479d34c18e1c3e213a356836b5))
* 🚀 固定依赖包版本 ([c2d3535](https://github.com/viarotel-org/vite-uniapp-template/commit/c2d35357679bc861aacc6e222f90eb32fc3b4899))
* 🚀 对小程序进行兼容性适配 ([1514898](https://github.com/viarotel-org/vite-uniapp-template/commit/1514898af9270fc9e1022d06a00cc8f02a1aa9c6))
* 🚀 重构路由方法 ([739b68e](https://github.com/viarotel-org/vite-uniapp-template/commit/739b68e8391ecdaf407a957a1e9636c8d7268996))
* 🚀 重构项目结构 ([54549de](https://github.com/viarotel-org/vite-uniapp-template/commit/54549de63da96703f28f3c3d66e078a38548e978))


### Miscellaneous Chores

* release 3.0.0 ([51d7e50](https://github.com/viarotel-org/vite-uniapp-template/commit/51d7e500c9f70051627d16ab520f1c8b75bf3f98))

## [2.7.3](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.7.2...v2.7.3) (2024-03-06)


### Bug Fixes

* 🐛 去除阶段模式环境变量 ([00e71c8](https://github.com/viarotel-org/vite-uniapp-template/commit/00e71c8731053a568b01c507950da51cb5c1f10e))

## [2.7.2](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.7.1...v2.7.2) (2024-03-06)


### Performance Improvements

* ♻️ 使用环境变量实现接口域名切换 ([b571d42](https://github.com/viarotel-org/vite-uniapp-template/commit/b571d422e431f905872c65c27e72228613f0546c))
* ♻️ 更新用户目录名称 ([c2b7f5e](https://github.com/viarotel-org/vite-uniapp-template/commit/c2b7f5ee57d1717408434f07f1f9fadd504a4f3b))

## [2.7.1](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.7.0...v2.7.1) (2024-03-05)


### Bug Fixes

* 🐛 修复 app 端登录问题 ([b5b03b1](https://github.com/viarotel-org/vite-uniapp-template/commit/b5b03b172f3e64a24aba18f86b605d08edb7ca6a))

## [2.7.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.6.1...v2.7.0) (2024-02-20)


### Features

* ✨ 启用新的 Eslint 配置 ([8dfeef3](https://github.com/viarotel-org/vite-uniapp-template/commit/8dfeef3095f7b8b01e5c867ab471239ec7990692))


### Bug Fixes

* 🐛 修复插件导致的编译错误 ([8b37bce](https://github.com/viarotel-org/vite-uniapp-template/commit/8b37bce8f66900c5c2ee6923ae0362aeb495cb4f))


### Performance Improvements

* ♻️ 启用 query-string  减小构建大小 并更新依赖版本 ([74de17a](https://github.com/viarotel-org/vite-uniapp-template/commit/74de17a256cd153fe4cd92687329bd850e2dfe85))
* ♻️ 性能优化及依赖更新 ([1eccb37](https://github.com/viarotel-org/vite-uniapp-template/commit/1eccb3771205695d73c9add6f7a2377bd88ba74f))
* ♻️ 文件结构及代码逻辑优化 ([3b66faf](https://github.com/viarotel-org/vite-uniapp-template/commit/3b66faf7fa49b5bce4855db07591dc87f2f4c62f))
* ✨ 使用 @uni-helper/unocss-preset-uni ([705c794](https://github.com/viarotel-org/vite-uniapp-template/commit/705c794dd8daa74814e7614ad1c6d56847e392be))
* 💄 更新属性名称 ([8698f52](https://github.com/viarotel-org/vite-uniapp-template/commit/8698f5233835d12ba3af09d24bb1295224c5def3))
* 💄 更新服务器配置文件名 ([07500b5](https://github.com/viarotel-org/vite-uniapp-template/commit/07500b5d862364f57973ba997f25896b313c1629))
* 📦️ 更新 unocss 版本 ([6839026](https://github.com/viarotel-org/vite-uniapp-template/commit/68390269e1c570debdf9e8810c8c6b9958cd38a2))

## [2.6.1](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.6.0...v2.6.1) (2023-12-20)


### Bug Fixes

* 🐛 修复打包完成后无法自动停止终端的问题 ([e19b491](https://github.com/viarotel-org/vite-uniapp-template/commit/e19b491fa34baee4796df787681698c7676eac7c))

## [2.6.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.5.5...v2.6.0) (2023-12-19)


### Features

* ✨ 采用新的 uv-ui 作为UI框架并更新依赖版本 ([44f5e98](https://github.com/viarotel-org/vite-uniapp-template/commit/44f5e988dec3bb31e124b643f7e500c60dffa111))


### Performance Improvements

* 📝 更新 Eslint 配置 ([5a2a92a](https://github.com/viarotel-org/vite-uniapp-template/commit/5a2a92a8f03fe4daae60b9a1ef9c2dca60fe0608))

## [2.5.5](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.5.4...v2.5.5) (2023-10-18)


### Bug Fixes

* 🚀 修复在 app 端环境下路由插件系统使用异常的问题 ([894ca30](https://github.com/viarotel-org/vite-uniapp-template/commit/894ca308d236fce5846e8348590cfbe1c01838c6))

## [2.5.4](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.5.3...v2.5.4) (2023-09-21)


### Bug Fixes

* 🔧 修复小程序端某些样式不生效的问题 ([e532395](https://github.com/viarotel-org/vite-uniapp-template/commit/e5323955809cf57d733064dfa2ebc14cc6f8f37f))

## [2.5.3](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.5.2...v2.5.3) (2023-09-14)


### Bug Fixes

* 🐛 修复小程序环境打包失败的问题 ([14c9645](https://github.com/viarotel-org/vite-uniapp-template/commit/14c9645d3c4b3abd248816087b3edd16e9973fc1))

## [2.5.2](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.5.1...v2.5.2) (2023-09-09)


### Bug Fixes

* 🔧 修复 uni-network 格式化参数的行为与 axios不一致的问题 ([00af3bf](https://github.com/viarotel-org/vite-uniapp-template/commit/00af3bfc50844ae45d5e860d86bf9aeabf3791d1))
* 🔧 修复打包后由于方法名混淆导致路由中间件无法匹配触发的问题 ([37515aa](https://github.com/viarotel-org/vite-uniapp-template/commit/37515aa0f526ae3810a979a347cc997443061fe4))
* 🔧 固定 qs 版本以解决不兼容微信小程序的问题 ([95f733e](https://github.com/viarotel-org/vite-uniapp-template/commit/95f733e8492e13688054739e6144b4fb39544696))

## [2.5.1](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.5.0...v2.5.1) (2023-09-09)


### Bug Fixes

* 🗑️ 去除登录页面冗余的空白顶栏 ([25bc3bd](https://github.com/viarotel-org/vite-uniapp-template/commit/25bc3bda88e8da69f8122e7b3e75422560f0b23e))

## [2.5.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.4.0...v2.5.0) (2023-09-09)


### Features

* 🚀 添加跳转外部网页功能 ([8d062a9](https://github.com/viarotel-org/vite-uniapp-template/commit/8d062a9d86126980181fb6e9ab0ca289f93b8c66))

## [2.4.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.3.3...v2.4.0) (2023-09-08)


### Features

* 🚀 对部分页面样式进行改进并去除冗余的控制台输出 ([e73ba19](https://github.com/viarotel-org/vite-uniapp-template/commit/e73ba1933c594ecc5e8ad0317d6d35345f9e972f))

## [2.3.3](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.3.2...v2.3.3) (2023-09-08)


### Bug Fixes

* 📈 修复入口页面模块引用格式不统一的问题 ([bd72731](https://github.com/viarotel-org/vite-uniapp-template/commit/bd72731cf866940aa4a4e1d84795bc035be05b8c))

## [2.3.2](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.3.1...v2.3.2) (2023-09-08)


### Bug Fixes

* 🔧 修复同步脚本错误的问题 ([73d802a](https://github.com/viarotel-org/vite-uniapp-template/commit/73d802abf100f853ae1c1f41a650090a483bfa3c))

## [2.3.1](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.3.0...v2.3.1) (2023-09-07)


### Bug Fixes

* 📝 修复文档描述错误 ([3325235](https://github.com/viarotel-org/vite-uniapp-template/commit/3325235dac5f0dac5301cbbafff111c1509548de))

## [2.3.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.2.1...v2.3.0) (2023-09-05)


### Features

* 🎨 主题色定制相关配置功能更新 ([bcadbfa](https://github.com/viarotel-org/vite-uniapp-template/commit/bcadbfaf583a283804bd1ebdd6b5846ae11f0fb0))

## [2.2.1](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.2.0...v2.2.1) (2023-09-01)


### Bug Fixes

* 🔧 修复使用 yarn 作为包管理器时启动项目报错的问题 ([1778bb9](https://github.com/viarotel-org/vite-uniapp-template/commit/1778bb9c4b56e097ba5cadc1ae6e37fd89357ca8))

## [2.2.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.1.1...v2.2.0) (2023-08-29)


### Features

* 🚀 登录示例页面调整 ([a0c1688](https://github.com/viarotel-org/vite-uniapp-template/commit/a0c16881e36e836ee7d9215dfec4615e1984a2bb))

## [2.1.1](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.1.0...v2.1.1) (2023-08-29)


### Bug Fixes

* bugfix ([9757722](https://github.com/viarotel-org/vite-uniapp-template/commit/97577229d7999f10a9efdcb3ef08efa8a3328cde))


### Performance Improvements

* 🚀 去除冗余的默认导出以降低生产包大小 ([88a529a](https://github.com/viarotel-org/vite-uniapp-template/commit/88a529a51541210c6a030fbb56ebc044173c0c28))

## [2.1.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.0.1...v2.1.0) (2023-08-28)


### Miscellaneous Chores

* release 2.1.0 ([ccc4cb1](https://github.com/viarotel-org/vite-uniapp-template/commit/ccc4cb19295420b632f61ae4e5424809e55dc7b8))

## [2.0.1](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.0.0...v2.0.1) (2023-08-14)


### Performance Improvements

* 🔧 调整路由导出方式以简化路由中间件定义方式 ([d0f9015](https://github.com/viarotel-org/vite-uniapp-template/commit/d0f901526adeed8ab60898a18d5ade046f14ceeb))

## [2.0.0](https://github.com/viarotel-org/vite-uniapp-template/compare/v2.0.0...v2.0.0) (2023-08-09)


### Features

* ✨ 样式优化及调整 ([c3d4a56](https://github.com/viarotel-org/vite-uniapp-template/commit/c3d4a56e836f4af4cc1d929d16f9b46319c69617))
* feat ([19894dc](https://github.com/viarotel-org/vite-uniapp-template/commit/19894dcbd075ba5181372df19c4e6c2387afa120))


### Bug Fixes

* :bug: bugfix ([2bf4391](https://github.com/viarotel-org/vite-uniapp-template/commit/2bf4391b96bd0b2e94acfcf7ee36a757d02808aa))
* :bug: 修复 uni-vite-plugin-h5-prod-effect 导致 h5 打包后无法正常运行的问题 ([cfe09f6](https://github.com/viarotel-org/vite-uniapp-template/commit/cfe09f65eecc4d9e1f7867a4280e2a54feb10158))
* 🐛 bugfix ([deaa1ec](https://github.com/viarotel-org/vite-uniapp-template/commit/deaa1ec2f283d9d54eb63e852ae1e30edb454dc1))
* 📝 bugfix ([91ed0eb](https://github.com/viarotel-org/vite-uniapp-template/commit/91ed0eb09ee84dd62769a00293667ea9d6ce5622))
* 📝 修复路由文件缺失导致报错的问题 ([4d51094](https://github.com/viarotel-org/vite-uniapp-template/commit/4d5109473f16c24530b797cd9aa6e48f36d862e9))

### Performance Improvements

* :ambulance: 减少生产环境下包大小 ([e1b67c6](https://github.com/viarotel-org/vite-uniapp-template/commit/e1b67c6fff1a2add11f498a0eba7bff70f794e4c))
* :memo: ([dd8dcf5](https://github.com/viarotel-org/vite-uniapp-template/commit/dd8dcf598dec5721e27c3e953535f33af1a627c1))
* 🎉 路由相关代码逻辑优化 ([101f828](https://github.com/viarotel-org/vite-uniapp-template/commit/101f828fcfdc8cdb04e29f6320d775fe84a2bfac))
* 📝 去除冗余代码 ([6cc02e9](https://github.com/viarotel-org/vite-uniapp-template/commit/6cc02e9baa3933695215a11457253b9cdcf2e2bd))
* 📝 去除冗余依赖 ([c98f33e](https://github.com/viarotel-org/vite-uniapp-template/commit/c98f33ef7e897640fa0b08fd1fda9dc9d5ed61e1))
* perf ([a48a7fc](https://github.com/viarotel-org/vite-uniapp-template/commit/a48a7fc7875f81a1e4d299004c69b7dafde29b99))
* 图标组件性能优化 ([1d2d89d](https://github.com/viarotel-org/vite-uniapp-template/commit/1d2d89d3708a72989a7fb4795a7c10d4ea076987))


### Miscellaneous Chores

* release 2.0.0 ([9990344](https://github.com/viarotel-org/vite-uniapp-template/commit/9990344751ab75dc77d2a1d7b00873b02148e656))
